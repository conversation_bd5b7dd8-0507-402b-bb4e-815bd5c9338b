using Microsoft.Extensions.Logging;
using UNI.Model;
using Uni.Personal.BLL.Helpers;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Contract service implementation
    /// </summary>
    public class ContractService : IContractService
    {
        private readonly IContractRepository _contractRepository;
        private readonly ICacheService? _cacheService;
        private readonly ILogger<ContractService> _logger;

        public ContractService(
            IContractRepository contractRepository,
            ILogger<ContractService> logger,
            ICacheService? cacheService = null)
        {
            _contractRepository = contractRepository;
            _cacheService = cacheService;
            _logger = logger;
        }

        /// <summary>
        /// Get paginated list of contracts with caching
        /// </summary>
        /// <param name="query">Filter input for contracts</param>
        /// <returns>Paginated list of contracts</returns>
        public async Task<CommonListPage> GetPageAsync(ContractFilterInput? query)
        {
            if (query == null)
            {
                return await _contractRepository.GetPageAsync(query);
            }

            // If caching is not available, fallback to direct repository call
            if (_cacheService == null)
            {
                _logger.LogDebug("Cache service not available, fetching data directly from repository");
                return await _contractRepository.GetPageAsync(query);
            }

            // Generate cache key based on filter parameters
            var cacheKey = CacheKeyHelper.GetContractPageKey(query);

            try
            {
                // Try to get data from cache first
                var cachedData = await _cacheService.GetAsync<CommonListPage>(cacheKey);
                if (cachedData != null)
                {
                    _logger.LogDebug("Contract page data found in cache with key: {CacheKey}", cacheKey);
                    return cachedData;
                }

                _logger.LogDebug("Contract page data not found in cache, fetching from repository");

                // If not in cache, get from repository
                var result = await _contractRepository.GetPageAsync(query);

                // Cache the result for future requests
                await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(30));
                _logger.LogDebug("Contract page data cached with key: {CacheKey}", cacheKey);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while accessing cache for contract page data. Falling back to repository.");
                // If caching fails, fallback to direct repository call
                return await _contractRepository.GetPageAsync(query);
            }
        }
    }
}
