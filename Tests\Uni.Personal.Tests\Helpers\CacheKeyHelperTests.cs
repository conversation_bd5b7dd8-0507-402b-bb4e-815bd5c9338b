using Uni.Personal.BLL.Helpers;
using Uni.Personal.Model;
using Xunit;

namespace Uni.Personal.Tests.Helpers
{
    public class CacheKeyHelperTests
    {
        [Fact]
        public void GetContractPageKey_WithSameFilter_ReturnsSameKey()
        {
            // Arrange
            var filter1 = new ContractFilterInput 
            { 
                ContractNo = "TEST123",
                CustomerName = "John Doe",
                pageSize = 10,
                offSet = 0
            };
            
            var filter2 = new ContractFilterInput 
            { 
                ContractNo = "TEST123",
                CustomerName = "John Doe",
                pageSize = 10,
                offSet = 0
            };

            // Act
            var key1 = CacheKeyHelper.GetContractPageKey(filter1);
            var key2 = CacheKeyHelper.GetContractPageKey(filter2);

            // Assert
            Assert.Equal(key1, key2);
            Assert.StartsWith("contract:page:", key1);
        }

        [Fact]
        public void GetContractPageKey_WithDifferentFilter_ReturnsDifferentKey()
        {
            // Arrange
            var filter1 = new ContractFilterInput 
            { 
                ContractNo = "TEST123",
                CustomerName = "John Do<PERSON>"
            };
            
            var filter2 = new ContractFilterInput 
            { 
                ContractNo = "TEST456",
                CustomerName = "Jane Smith"
            };

            // Act
            var key1 = CacheKeyHelper.GetContractPageKey(filter1);
            var key2 = CacheKeyHelper.GetContractPageKey(filter2);

            // Assert
            Assert.NotEqual(key1, key2);
            Assert.StartsWith("contract:page:", key1);
            Assert.StartsWith("contract:page:", key2);
        }

        [Fact]
        public void GetContractPageKey_WithEmptyFilter_ReturnsValidKey()
        {
            // Arrange
            var filter = new ContractFilterInput();

            // Act
            var key = CacheKeyHelper.GetContractPageKey(filter);

            // Assert
            Assert.NotNull(key);
            Assert.StartsWith("contract:page:", key);
            Assert.True(key.Length > "contract:page:".Length);
        }

        [Fact]
        public void GetContractPagePattern_ReturnsCorrectPattern()
        {
            // Act
            var pattern = CacheKeyHelper.GetContractPagePattern();

            // Assert
            Assert.Equal("contract:page:*", pattern);
        }

        [Fact]
        public void GetContractPageKey_WithNullValues_HandlesGracefully()
        {
            // Arrange
            var filter = new ContractFilterInput 
            { 
                ContractNo = null,
                CustomerName = null,
                OrderCode = null
            };

            // Act
            var key = CacheKeyHelper.GetContractPageKey(filter);

            // Assert
            Assert.NotNull(key);
            Assert.StartsWith("contract:page:", key);
        }
    }
}
