using Microsoft.Extensions.Logging;
using Moq;
using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.BLL.Services;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;
using Xunit;

namespace Uni.Personal.Tests.Services
{
    public class ContractServiceTests
    {
        private readonly Mock<IContractRepository> _mockRepository;
        private readonly Mock<ICacheService> _mockCacheService;
        private readonly Mock<ILogger<ContractService>> _mockLogger;
        private readonly ContractService _contractService;

        public ContractServiceTests()
        {
            _mockRepository = new Mock<IContractRepository>();
            _mockCacheService = new Mock<ICacheService>();
            _mockLogger = new Mock<ILogger<ContractService>>();
            
            _contractService = new ContractService(
                _mockRepository.Object,
                _mockLogger.Object,
                _mockCacheService.Object);
        }

        [Fact]
        public async Task GetPageAsync_WithNullQuery_CallsRepositoryDirectly()
        {
            // Arrange
            ContractFilterInput? query = null;
            var expectedResult = new CommonListPage();
            _mockRepository.Setup(r => r.GetPageAsync(query))
                          .ReturnsAsync(expectedResult);

            // Act
            var result = await _contractService.GetPageAsync(query);

            // Assert
            Assert.Equal(expectedResult, result);
            _mockRepository.Verify(r => r.GetPageAsync(query), Times.Once);
            _mockCacheService.Verify(c => c.GetAsync<CommonListPage>(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task GetPageAsync_WithCacheHit_ReturnsCachedData()
        {
            // Arrange
            var query = new ContractFilterInput { ContractNo = "TEST123" };
            var cachedResult = new CommonListPage();
            
            _mockCacheService.Setup(c => c.GetAsync<CommonListPage>(It.IsAny<string>()))
                           .ReturnsAsync(cachedResult);

            // Act
            var result = await _contractService.GetPageAsync(query);

            // Assert
            Assert.Equal(cachedResult, result);
            _mockCacheService.Verify(c => c.GetAsync<CommonListPage>(It.IsAny<string>()), Times.Once);
            _mockRepository.Verify(r => r.GetPageAsync(It.IsAny<ContractFilterInput>()), Times.Never);
        }

        [Fact]
        public async Task GetPageAsync_WithCacheMiss_FetchesFromRepositoryAndCaches()
        {
            // Arrange
            var query = new ContractFilterInput { ContractNo = "TEST123" };
            var repositoryResult = new CommonListPage();
            
            _mockCacheService.Setup(c => c.GetAsync<CommonListPage>(It.IsAny<string>()))
                           .ReturnsAsync((CommonListPage?)null);
            
            _mockRepository.Setup(r => r.GetPageAsync(query))
                          .ReturnsAsync(repositoryResult);

            // Act
            var result = await _contractService.GetPageAsync(query);

            // Assert
            Assert.Equal(repositoryResult, result);
            _mockCacheService.Verify(c => c.GetAsync<CommonListPage>(It.IsAny<string>()), Times.Once);
            _mockRepository.Verify(r => r.GetPageAsync(query), Times.Once);
            _mockCacheService.Verify(c => c.SetAsync(It.IsAny<string>(), repositoryResult, It.IsAny<TimeSpan>()), Times.Once);
        }

        [Fact]
        public async Task GetPageAsync_WithCacheException_FallsBackToRepository()
        {
            // Arrange
            var query = new ContractFilterInput { ContractNo = "TEST123" };
            var repositoryResult = new CommonListPage();
            
            _mockCacheService.Setup(c => c.GetAsync<CommonListPage>(It.IsAny<string>()))
                           .ThrowsAsync(new Exception("Cache error"));
            
            _mockRepository.Setup(r => r.GetPageAsync(query))
                          .ReturnsAsync(repositoryResult);

            // Act
            var result = await _contractService.GetPageAsync(query);

            // Assert
            Assert.Equal(repositoryResult, result);
            _mockRepository.Verify(r => r.GetPageAsync(query), Times.Once);
        }

        [Fact]
        public async Task GetPageAsync_WithoutCacheService_CallsRepositoryDirectly()
        {
            // Arrange
            var query = new ContractFilterInput { ContractNo = "TEST123" };
            var expectedResult = new CommonListPage();
            
            var serviceWithoutCache = new ContractService(
                _mockRepository.Object,
                _mockLogger.Object,
                null); // No cache service
            
            _mockRepository.Setup(r => r.GetPageAsync(query))
                          .ReturnsAsync(expectedResult);

            // Act
            var result = await serviceWithoutCache.GetPageAsync(query);

            // Assert
            Assert.Equal(expectedResult, result);
            _mockRepository.Verify(r => r.GetPageAsync(query), Times.Once);
        }
    }
}
