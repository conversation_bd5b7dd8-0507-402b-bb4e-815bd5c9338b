{"ConnectionStrings": {"UniPersonalConnection": "Server=***********;Database=dbUniMaster;Trusted_Connection=True;MultipleActiveResultSets=true;Integrated Security=false;User Id=sa;Password=**********;"}, "Jwt": {"Authority": "https://idp-dev.unicloudgroup.com.vn/realms/realm_unipersonal", "Audience": "swagger_developer", "RequireHttpsMetadata": false, "ValidateAudience": false, "ValidateIssuer": true, "ValidateLifetime": true}, "Redis": {"ConnectionString": "localhost:6379", "InstanceName": "UniPersonalPortal_Dev", "DefaultExpiration": "00:30:00"}, "Swagger": {"client_id": "swagger_developer", "scopes": ["openid", "profile", "email"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ExternalStorage": {"DefaultProvider": "MinIO", "EnableFallback": false, "FallbackOrder": [], "MinIO": {"Endpoint": "storage-dev.unicloudgroup.com.vn", "ProxyEndpoint": "", "AccessKey": "iZJWUfthMR4McKt6VvBO", "SecretKey": "jUyIENNflJeHzMS4SH9qh5nWR7b6h47DMLaLiXcE", "UseSSL": true, "Region": "", "DefaultBucket": "unimaster-personal-dev", "PrefixFolder": "", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}}}