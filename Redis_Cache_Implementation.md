# Redis Cache Implementation for GetPage Method

## Overview
This implementation adds Redis caching to the `GetPage` method in the ContractController to improve performance by caching frequently requested data.

## Components Added

### 1. Redis Configuration
- **File**: `Uni.Personal.Model/Configuration/RedisConfiguration.cs`
- **Purpose**: Configuration model for Redis settings
- **Properties**:
  - `ConnectionString`: Redis server connection string
  - `InstanceName`: Redis instance name for key prefixing
  - `DefaultExpiration`: Default cache expiration time (30 minutes)

### 2. Cache Service Interface
- **File**: `Uni.Personal.BLL/Interfaces/ICacheService.cs`
- **Purpose**: Abstraction for caching operations
- **Methods**:
  - `GetAsync<T>`: Retrieve cached data
  - `SetAsync<T>`: Store data in cache
  - `RemoveAsync`: Remove specific cache entry
  - `RemoveByPatternAsync`: Remove cache entries by pattern
  - `ExistsAsync`: Check if cache key exists

### 3. Cache Service Implementation
- **File**: `Uni.Personal.BLL/Services/CacheService.cs`
- **Purpose**: Redis-based implementation of ICacheService
- **Features**:
  - JSON serialization/deserialization
  - Configurable expiration times
  - Error handling with fallback

### 4. Cache Key Helper
- **File**: `Uni.Personal.BLL/Helpers/CacheKeyHelper.cs`
- **Purpose**: Generate consistent cache keys
- **Features**:
  - MD5 hash of filter parameters
  - Structured key naming convention
  - Pattern support for bulk operations

### 5. Updated Contract Service
- **File**: `Uni.Personal.BLL/Services/ContractService.cs`
- **Changes**:
  - Added optional ICacheService dependency
  - Implemented cache-first strategy
  - Added fallback to repository on cache failures
  - Added logging for cache operations

## Configuration

### appsettings.json
```json
{
  "Redis": {
    "ConnectionString": "localhost:6379",
    "InstanceName": "UniPersonalPortal",
    "DefaultExpiration": "00:30:00"
  }
}
```

### appsettings.Development.json
```json
{
  "Redis": {
    "ConnectionString": "localhost:6379",
    "InstanceName": "UniPersonalPortal_Dev",
    "DefaultExpiration": "00:30:00"
  }
}
```

## How It Works

1. **Cache Key Generation**: When `GetPageAsync` is called, a unique cache key is generated based on the filter parameters using MD5 hashing.

2. **Cache Lookup**: The service first checks if data exists in Redis cache using the generated key.

3. **Cache Hit**: If data is found in cache, it's deserialized and returned immediately.

4. **Cache Miss**: If data is not in cache:
   - Data is fetched from the repository
   - Result is cached for future requests (30 minutes default)
   - Data is returned to the client

5. **Error Handling**: If Redis is unavailable or any cache operation fails, the service falls back to direct repository access.

## Benefits

- **Performance**: Reduces database load for frequently requested data
- **Scalability**: Improves response times for repeated queries
- **Resilience**: Graceful fallback when cache is unavailable
- **Flexibility**: Configurable expiration times and cache keys

## Cache Key Format

Cache keys follow the pattern: `contract:page:{hash}`

Where `{hash}` is an MD5 hash of the serialized filter parameters, ensuring:
- Unique keys for different filter combinations
- Consistent keys for identical filter parameters
- Reasonable key length regardless of filter complexity

## Dependencies Added

### NuGet Packages
- `Microsoft.Extensions.Caching.StackExchangeRedis` (8.0.16)
- `StackExchange.Redis` (2.8.16)
- `Microsoft.Extensions.Caching.Abstractions` (8.0.0)
- `Microsoft.Extensions.Logging.Abstractions` (8.0.2)
- `Microsoft.Extensions.Options` (8.0.2)

## Usage Example

The caching is transparent to the controller. The existing `GetPage` endpoint automatically benefits from caching:

```http
GET /api/v1/Contract/GetPage?pageSize=10&offSet=0&contractNo=ABC123
```

First request: Data fetched from database and cached
Subsequent requests: Data served from cache (until expiration)

## Monitoring and Debugging

The implementation includes logging at debug level:
- Cache hits and misses
- Cache key generation
- Fallback scenarios
- Error conditions

Enable debug logging to monitor cache performance:

```json
{
  "Logging": {
    "LogLevel": {
      "Uni.Personal.BLL.Services.ContractService": "Debug"
    }
  }
}
```

## Future Enhancements

1. **Cache Invalidation**: Implement cache invalidation when contract data is modified
2. **Pattern-based Removal**: Complete implementation of `RemoveByPatternAsync` for bulk cache clearing
3. **Cache Metrics**: Add performance metrics and monitoring
4. **Distributed Locking**: Implement distributed locking for cache-aside pattern
5. **Compression**: Add compression for large cached objects
