namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// Cache service interface for managing cached data
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Get cached data by key
        /// </summary>
        /// <typeparam name="T">Type of cached data</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached data or null if not found</returns>
        Task<T?> GetAsync<T>(string key) where T : class;

        /// <summary>
        /// Set data in cache with expiration
        /// </summary>
        /// <typeparam name="T">Type of data to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="data">Data to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        /// <returns>Task</returns>
        Task SetAsync<T>(string key, T data, TimeSpan? expiration = null) where T : class;

        /// <summary>
        /// Remove data from cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Task</returns>
        Task RemoveAsync(string key);

        /// <summary>
        /// Remove data from cache by pattern
        /// </summary>
        /// <param name="pattern">Cache key pattern</param>
        /// <returns>Task</returns>
        Task RemoveByPatternAsync(string pattern);

        /// <summary>
        /// Check if key exists in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if key exists</returns>
        Task<bool> ExistsAsync(string key);
    }
}
