using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Options;
using System.Text.Json;
using Uni.Personal.Model.Configuration;
using Uni.Personal.BLL.Interfaces;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Redis cache service implementation
    /// </summary>
    public class CacheService : ICacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly RedisConfiguration _redisConfig;

        public CacheService(IDistributedCache distributedCache, IOptions<RedisConfiguration> redisConfig)
        {
            _distributedCache = distributedCache;
            _redisConfig = redisConfig.Value;
        }

        /// <summary>
        /// Get cached data by key
        /// </summary>
        /// <typeparam name="T">Type of cached data</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached data or null if not found</returns>
        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            var cachedData = await _distributedCache.GetStringAsync(key);
            if (string.IsNullOrEmpty(cachedData))
            {
                return null;
            }

            return JsonSerializer.Deserialize<T>(cachedData);
        }

        /// <summary>
        /// Set data in cache with expiration
        /// </summary>
        /// <typeparam name="T">Type of data to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="data">Data to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        /// <returns>Task</returns>
        public async Task SetAsync<T>(string key, T data, TimeSpan? expiration = null) where T : class
        {
            var serializedData = JsonSerializer.Serialize(data);
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration ?? _redisConfig.DefaultExpiration
            };

            await _distributedCache.SetStringAsync(key, serializedData, options);
        }

        /// <summary>
        /// Remove data from cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Task</returns>
        public async Task RemoveAsync(string key)
        {
            await _distributedCache.RemoveAsync(key);
        }

        /// <summary>
        /// Remove data from cache by pattern
        /// </summary>
        /// <param name="pattern">Cache key pattern</param>
        /// <returns>Task</returns>
        public async Task RemoveByPatternAsync(string pattern)
        {
            // Note: Pattern-based removal requires direct Redis access
            // For now, this is a placeholder implementation
            // In a production environment, you might want to use StackExchange.Redis directly
            await Task.CompletedTask;
        }

        /// <summary>
        /// Check if key exists in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if key exists</returns>
        public async Task<bool> ExistsAsync(string key)
        {
            var cachedData = await _distributedCache.GetStringAsync(key);
            return !string.IsNullOrEmpty(cachedData);
        }
    }
}
