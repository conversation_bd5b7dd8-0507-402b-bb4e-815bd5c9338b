using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Helpers
{
    /// <summary>
    /// Helper class for generating cache keys
    /// </summary>
    public static class CacheKeyHelper
    {
        private const string CONTRACT_PAGE_PREFIX = "contract:page";

        /// <summary>
        /// Generate cache key for contract page data
        /// </summary>
        /// <param name="filter">Contract filter input</param>
        /// <returns>Cache key</returns>
        public static string GetContractPageKey(ContractFilterInput filter)
        {
            // Create a hash of the filter parameters to ensure unique cache keys
            var filterJson = JsonSerializer.Serialize(filter, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            });

            var hash = ComputeHash(filterJson);
            return $"{CONTRACT_PAGE_PREFIX}:{hash}";
        }

        /// <summary>
        /// Generate cache key pattern for contract page data
        /// </summary>
        /// <returns>Cache key pattern</returns>
        public static string GetContractPagePattern()
        {
            return $"{CONTRACT_PAGE_PREFIX}:*";
        }

        /// <summary>
        /// Compute MD5 hash of input string
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>Hash string</returns>
        private static string ComputeHash(string input)
        {
            using var md5 = MD5.Create();
            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = md5.ComputeHash(inputBytes);
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }
    }
}
